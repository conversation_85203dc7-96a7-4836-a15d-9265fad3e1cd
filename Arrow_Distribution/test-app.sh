#!/bin/bash

# Test script for Arrow ----------> A application
# This script helps verify that the built application works correctly

echo "🚀 Arrow ----------> A - Application Test Script"
echo "================================================"
echo ""

# Check if .dmg files exist
echo "📦 Checking DMG files..."
if [ -f "Arrow ---------- A-1.0.0-x64.dmg" ]; then
    echo "✅ Intel x64 DMG found: $(ls -lh 'Arrow ---------- A-1.0.0-x64.dmg' | awk '{print $5}')"
else
    echo "❌ Intel x64 DMG not found"
fi

if [ -f "Arrow ---------- A-1.0.0-arm64.dmg" ]; then
    echo "✅ ARM64 DMG found: $(ls -lh 'Arrow ---------- A-1.0.0-arm64.dmg' | awk '{print $5}')"
else
    echo "❌ ARM64 DMG not found"
fi

echo ""

# Check if app bundles exist
echo "📱 Checking Application Bundles..."
if [ -d "mac/Arrow ---------- A.app" ]; then
    echo "✅ Intel x64 app bundle found"
else
    echo "❌ Intel x64 app bundle not found"
fi

if [ -d "mac-arm64/Arrow ---------- A.app" ]; then
    echo "✅ ARM64 app bundle found"
else
    echo "❌ ARM64 app bundle not found"
fi

echo ""

# Check .env file in app bundle
echo "🔑 Checking Environment Variables..."
if [ -f "mac/Arrow ---------- A.app/Contents/Resources/.env" ]; then
    echo "✅ .env file found in Intel app bundle"
    echo "📋 Environment variables:"
    grep -E "^[A-Z_]+=.*" "mac/Arrow ---------- A.app/Contents/Resources/.env" | sed 's/=.*/=***HIDDEN***/'
else
    echo "❌ .env file not found in Intel app bundle"
fi

echo ""

# Test launching the application
echo "🧪 Testing Application Launch..."
echo "Attempting to launch the application..."
echo "(You should see the Arrow application window open)"

# Detect architecture and launch appropriate version
ARCH=$(uname -m)
if [ "$ARCH" = "arm64" ]; then
    echo "🔍 Detected Apple Silicon Mac - launching ARM64 version"
    open "mac-arm64/Arrow ---------- A.app"
else
    echo "🔍 Detected Intel Mac - launching x64 version"
    open "mac/Arrow ---------- A.app"
fi

echo ""
echo "✨ Test completed!"
echo ""
echo "📝 Manual verification steps:"
echo "1. Check that the Arrow application window opened"
echo "2. Verify that AI models are working (try Gemini or Claude)"
echo "3. Test screenshot functionality (⌘+H)"
echo "4. Test keyboard shortcuts"
echo "5. Verify window stays on top of other applications"
echo ""
echo "🎯 If all tests pass, the .dmg files are ready for distribution!"
