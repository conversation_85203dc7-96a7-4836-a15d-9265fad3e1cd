directories:
  output: ../Arrow_Distribution
  buildResources: build
appId: com.arrow.interview-assistant
productName: Arrow
files:
  - filter:
      - dist/**/*
      - node_modules/**/*
      - package.json
      - .env
extraResources:
  - from: .env
    to: .env
mac:
  category: public.app-category.developer-tools
  target:
    - target: dmg
      arch:
        - x64
        - arm64
  entitlements: build/entitlements.mac.plist
  entitlementsInherit: build/entitlements.mac.plist
  artifactName: ${productName}-${version}-${arch}.${ext}
dmg:
  title: Arrow
  artifactName: ${productName}-${version}-${arch}.${ext}
  background: null
  contents:
    - x: 130
      'y': 220
    - x: 410
      'y': 220
      type: link
      path: /Applications
win:
  target: nsis
linux:
  target: AppImage
electronVersion: 21.4.4
