# Arrow ----------> A - macOS Distribution

## Overview

This directory contains the distributable macOS application files for the **Arrow ----------> A** Interview Assistant application.

## Files Created

### DMG Files (Ready for Distribution)
- **`Arrow-1.0.0-x64.dmg`** - Intel x64 version (for Intel Macs)
- **`Arrow-1.0.0-arm64.dmg`** - Apple Silicon ARM64 version (for M1/M2/M3 Macs)

### Application Bundles
- **`mac/Arrow.app`** - Intel x64 application bundle
- **`mac-arm64/Arrow.app`** - ARM64 application bundle

### Additional Files
- **`*.dmg.blockmap`** - Block map files for efficient updates
- **`latest-mac.yml`** - Auto-updater configuration
- **`builder-*.yml`** - Build configuration files

## Installation Instructions

### For End Users:

1. **Choose the correct version for your Mac:**
   - Intel Mac: Use `Arrow-1.0.0-x64.dmg`
   - Apple Silicon Mac (M1/M2/M3): Use `Arrow-1.0.0-arm64.dmg`

2. **Install the application:**
   - Double-click the appropriate .dmg file
   - Drag "Arrow" to your Applications folder
   - The app includes all necessary API keys pre-configured

3. **First Launch:**
   - You may see a security warning (app is not code-signed)
   - Go to System Preferences > Security & Privacy > General
   - Click "Open Anyway" next to the blocked app message

## Features Included

- **AI-Powered Interview Assistant** with multiple model support:
  - ✅ Gemini (Google) - Pre-configured and ready
  - ✅ Claude (Anthropic) - Pre-configured and ready
  - ⚠️ OpenAI GPT - Requires user's own API key
- **Screenshot Analysis** for coding problems
- **Always-on-top** window for easy access during interviews
- **Keyboard shortcuts** for quick actions
- **Pre-configured API keys** bundled in the application

## Environment Variables

The application comes with API keys pre-configured in the bundled `.env` file:
- **Gemini API Key**: ✅ Included and active
- **Claude API Key**: ✅ Included and active
- **OpenAI API Key**: ❌ Not configured (user can add their own)

## System Requirements

- macOS 10.12 or later
- Intel Mac or Apple Silicon Mac
- Internet connection for AI model access
- Screen recording permissions (for screenshot functionality)

## Build Information

- **Build Date**: $(date)
- **Electron Version**: 21.4.4
- **Node.js Version**: $(node --version)
- **Application Version**: 1.0.0
- **Build Tool**: electron-builder 23.6.0

## Security Notes

⚠️ **Important**: This application is not code-signed, so macOS will show security warnings on first launch. This is normal for development/testing distributions.

For production use, the application should be:
1. Code-signed with a valid Apple Developer certificate
2. Notarized through Apple's notarization service
3. Distributed through official channels

## Testing Checklist

Before distributing, verify:
- [ ] Application launches successfully
- [ ] .env file is properly loaded (check API keys work)
- [ ] Screenshot functionality works
- [ ] Keyboard shortcuts function correctly
- [ ] Window management works as expected
- [ ] AI models respond correctly (Gemini and Claude)

## Distribution Notes

- **Do NOT commit these files to git** - they are for testing only
- Files are created in a separate directory to avoid accidental commits
- Test thoroughly before any public distribution
- Consider creating proper code-signed versions for production

---

**Note**: This is a development/testing distribution. The included API keys are for demonstration and may have usage limits.
