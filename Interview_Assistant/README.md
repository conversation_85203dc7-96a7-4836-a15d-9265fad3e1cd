# Interview Assistant

A desktop application to help with technical interviews. The application provides real-time coding assistance and remains invisible during screen sharing.

## Features

- Screenshot capture of coding problems
- AI-powered problem analysis and solution generation
- Invisible during screen sharing
- Keyboard shortcuts for easy control
- Support for both OpenAI and Google Gemini AI

## Prerequisites

- Node.js (v14 or higher)
- npm (Node Package Manager)
- An API key from either OpenAI or Google Gemini (or both)

## Installation

1. Clone the repository:
```bash
git clone https://github.com/akashiitd/Interview_Assistant.git
cd Interview_Assistant
```

2. Install dependencies:
```bash
npm install
```

3. Set up your API keys:
   - Create a `.env` file in the root directory
   - Add your API key(s):
```env
# OpenAI API key from https://platform.openai.com/account/api-keys
OPENAI_API_KEY=your-openai-key-here

# Gemini API key from https://makersuite.google.com/app/apikey
GEMINI_API_KEY=your-gemini-key-here
```

## Running the Application

### Development Mode
```bash
npm run dev
```

### Production Build
```bash
npm run build
npm start
```

## Keyboard Shortcuts

- `⌘ + H`: Take screenshot of the coding problem
- `⌘ + B`: Toggle window visibility
- `⌘ + Arrow keys`: Move window position
- `⌘ + Enter`: Generate solution
- `⌘ + R`: Reset session
- `⌘ + Q`: Quit application

## Usage Instructions

1. Start the application using `npm run dev`
2. Position the window where you want it using `⌘ + Arrow keys`
3. When you encounter a coding problem:
   - Press `⌘ + H` to capture the problem
   - The AI will automatically analyze it and provide a solution
   - Use `⌘ + B` to toggle visibility during interviews
4. Press `⌘ + R` to reset and start with a new problem
5. Press `⌘ + Q` to quit the application

## Troubleshooting

1. If you see API errors:
   - Make sure you've added your API key correctly in the `.env` file
   - Check that the API key is valid and has sufficient credits
   - Restart the application after updating the `.env` file

2. If the window is not visible:
   - Press `⌘ + B` to toggle visibility
   - Check if the window is off-screen using `⌘ + Arrow keys`
   - Use `⌘ + R` to reset the application state

3. If screenshots aren't working:
   - Make sure you've granted screen recording permissions to the application
   - Try restarting the application

## Development Commands

- `npm run dev`: Start in development mode with hot reload
- `npm start`: Run the compiled application
- `npm run build`: Create a production build
- `npm run compile`: Compile TypeScript files
- `npm run watch`: Watch for file changes (development)
