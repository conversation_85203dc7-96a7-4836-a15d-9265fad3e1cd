import Anthropic from '@anthropic-ai/sdk';

const anthropic = new Anthropic({
  apiKey: process.env.ANTHROPIC_API_KEY || ''
});

export async function analyzeWithClaude(screenshots: string[], prompt: string) {
  try {
    const content = [
      {
        type: "text" as const,
        text: prompt || "Analyze this coding problem and provide a solution with detailed explanations. First provide a brute force solution with code and explanation for each line, then provide an optimized solution with code and explanation for each line."
      },
      ...screenshots.map(screenshot => ({
        type: "image" as const,
        source: {
          type: "base64" as const,
          media_type: "image/jpeg" as const,
          data: screenshot
        }
      }))
    ];

    const message = await anthropic.messages.create({
      model: "claude-3-sonnet-20240229",
      max_tokens: 4096,
      messages: [
        {
          role: "user",
          content
        }
      ]
    });

    const responseText = message.content.find(block => block.type === 'text');
    if (!responseText || responseText.type !== 'text') {
      throw new Error('Unexpected response format from <PERSON>');
    }

    return { solution: responseText.text };
  } catch (error) {
    console.error('Claude API error:', error);
    throw error;
  }
} 