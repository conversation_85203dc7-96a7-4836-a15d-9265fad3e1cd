import { globalShortcut, app } from 'electron';
import { captureScreenshot } from './screenshot';
import { WindowManager } from './windowManager';

export function registerShortcuts(windowManager: WindowManager): void {
  // Command + G: Generate response
  globalShortcut.register('CommandOrControl+G', () => {
    if (windowManager.mainWindow) {
      windowManager.mainWindow.webContents.send('generate-prompt');
    }
  });

  // Command + H: Take screenshot
  globalShortcut.register('CommandOrControl+H', async () => {
    console.log('Screenshot shortcut triggered');
    try {
      const screenshotData = await captureScreenshot();
      console.log('Screenshot captured successfully');
      if (windowManager.mainWindow) {
        console.log('Sending screenshot to renderer');
        windowManager.mainWindow.webContents.send('screenshot-taken', screenshotData);
      }
    } catch (error: any) {
      console.error('Screenshot failed:', error);
      if (windowManager.mainWindow) {
        windowManager.mainWindow.webContents.send('screenshot-error', error?.message || 'Screenshot failed');
      }
    }
  });
  
  // Command + B: Toggle visibility
  globalShortcut.register('CommandOrControl+B', () => {
    windowManager.toggleVisibility();
  });
  
  // Command + Arrow keys: Move window
  globalShortcut.register('CommandOrControl+Up', () => {
    windowManager.moveActiveWindow('up');
  });
  
  globalShortcut.register('CommandOrControl+Down', () => {
    windowManager.moveActiveWindow('down');
  });
  
  globalShortcut.register('CommandOrControl+Left', () => {
    windowManager.moveActiveWindow('left');
  });
  
  globalShortcut.register('CommandOrControl+Right', () => {
    windowManager.moveActiveWindow('right');
  });
  
  // Command + Enter: Provide solution
  globalShortcut.register('CommandOrControl+Enter', () => {
    if (windowManager.mainWindow) {
      windowManager.mainWindow.webContents.send('provide-solution');
    }
  });
  
  // Command + R: Reset
  globalShortcut.register('CommandOrControl+R', () => {
    console.log('Reset shortcut triggered');
    if (windowManager.mainWindow) {
      windowManager.mainWindow.webContents.send('reset-app');
    }
  });
  
  // Command + Q: Quit application
  globalShortcut.register('CommandOrControl+Q', () => {
    app.quit();
  });
  
  // Clean up shortcuts on app quit
  app.on('will-quit', () => {
    globalShortcut.unregisterAll();
  });
} 