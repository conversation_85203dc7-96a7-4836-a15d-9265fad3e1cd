import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './components/App';
import './styles.css';

document.addEventListener('DOMContentLoaded', () => {
  const container = document.getElementById('app');
  if (!container) {
    throw new Error("Root element with id 'app' not found");
  }
  
  const root = ReactDOM.createRoot(container);
  root.render(React.createElement(App));
}); 