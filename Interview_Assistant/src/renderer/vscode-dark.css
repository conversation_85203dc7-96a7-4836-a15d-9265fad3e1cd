/**
 * VS Code Dark Theme for Prism.js
 */

code[class*="language-"],
pre[class*="language-"] {
  color: #D4D4D4;
  background: none;
  font-family: '<PERSON><PERSON>', 'Monaco', 'Courier New', monospace;
  font-size: 16px; /* Increased from 14px for better readability */
  text-align: left;
  white-space: pre-wrap; /* Changed from pre to pre-wrap to allow wrapping */
  word-spacing: normal;
  word-break: break-word; /* Changed from normal to break-word to prevent overflow */
  word-wrap: break-word; /* Added to ensure text wraps */
  line-height: 1.5;
  tab-size: 4;
  hyphens: none;
  width: 100%; /* Ensure it takes full width */
}

/* Code blocks */
pre[class*="language-"] {
  padding: 1em;
  margin: 0;
  overflow: visible; /* Changed from auto to visible to prevent scrollbars */
  background: #1E1E1E;
  width: 100%; /* Ensure it takes full width */
  box-sizing: border-box; /* Include padding in width calculation */
}

/* Inline code */
:not(pre) > code[class*="language-"] {
  padding: .1em;
  border-radius: .3em;
  white-space: normal;
}

.token.comment,
.token.prolog,
.token.doctype,
.token.cdata {
  color: #6A9955;
}

.token.decorator {
  color: #569CD6;
}

.token.string {
  color: #CE9178;
}

.token.keyword {
  color: #C586C0;
}

.token.function {
  color: #DCDCAA;
}

.token.parameter,
.token.variable {
  color: #9CDCFE;
}

.token.operator,
.token.punctuation {
  color: #D4D4D4;
}

.token.class-name,
.token.builtin {
  color: #4EC9B0;
}

.token.number {
  color: #B5CEA8;
}

.token.boolean {
  color: #569CD6;
}

.token.property {
  color: #9CDCFE;
}

/* Python specific */
.language-python .token.decorator {
  color: #569CD6;
}

.language-python .token.decorator .name {
  color: #DCDCAA;
}

.language-python .token.decorator .punctuation {
  color: #569CD6;
}

.language-python .token.function-variable {
  color: #DCDCAA;
}

.language-python .token.def {
  color: #C586C0;
}

.language-python .token.return {
  color: #C586C0;
}