/* Global cursor styles */
* {
  box-sizing: border-box;
  cursor: default !important;
}

button,
a,
.radio-label,
.radio-input,
.model-select,
.control-button,
.radio-text {
  cursor: default !important;
}

/* Override text cursor for all input elements */
input,
select,
textarea {
  cursor: default !important;
}

html {
  background: transparent;
  height: 100%;
  overflow: hidden;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  margin: 0;
  padding: 0;
  background-color: rgba(25, 25, 30, 0.85);
  color: #f0f0f0;
  border-radius: 8px;
  overflow: hidden;
  height: 100vh;
  width: 100vw;
  position: relative;
}

.app {
  padding: 10px;
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  box-sizing: border-box;
  overflow-y: auto; /* Allow vertical scrolling */
  overflow-x: hidden; /* Prevent horizontal scrolling */
  gap: 8px; /* Reduced gap to give more space to the analysis container */
}

/* Shortcuts bar at the top */
.shortcuts-bar {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 8px;
  width: 100%;
  background-color: #1e1e1e;
  padding: 6px 10px;
  margin-bottom: 10px;
  font-size: 9px;
  color: #d4d4d4;
  font-family: 'Menlo', 'Monaco', 'Courier New', monospace;
  line-height: 1.2;
  text-align: center;
  border-radius: 0;
}

.shortcuts-bar span {
  white-space: nowrap;
}

.app-title {
  text-align: center;
  margin-bottom: 10px;
}

.app-title h2 {
  margin: 0;
  font-size: 20px;
  color: #ffffff;
}

.button-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
  width: 100%;
  margin: 5px 0;
  flex-shrink: 0;
  min-height: 38px;
  z-index: 100;
}

.control-button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  background-color: #333;
  color: white;
  cursor: default !important;
  font-size: 14px;
  transition: all 0.2s ease;
  margin: 0 5px;
  height: 40px;
  display: block;
  width: 100%;
}

.control-button:disabled {
  opacity: 0.5;
  cursor: default !important;
}

.generate-button {
  background-color: #4c8bf5;
  position: relative;
  z-index: 3;
}

.generate-button.ready {
  background-color: #34a853;
  animation: pulse 2s infinite;
}

.generate-button:hover:not(:disabled) {
  background-color: #357abd;
}

.generate-button.ready:hover:not(:disabled) {
  background-color: #2d8a46;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(52, 168, 83, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(52, 168, 83, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(52, 168, 83, 0);
  }
}

.screenshot-button {
  background-color: #666;
}

.screenshot-button:hover:not(:disabled) {
  background-color: #555;
}

.reset-button {
  background-color: #d93025;
}

.reset-button:hover:not(:disabled) {
  background-color: #b3261e;
}

.screenshots-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr); /* Show 3 screenshots per row */
  gap: 10px;
  width: 100%;
}

.screenshot-item {
  position: relative;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  padding: 10px;
  transition: transform 0.2s;
  max-width: 100%;
}

.screenshot-item:hover {
  transform: scale(1.02);
}

.screenshot-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  color: #fff;
  font-size: 12px;
  font-weight: 500;
}

.remove-screenshot {
  background: none;
  border: none;
  color: #ff4444;
  font-size: 20px;
  cursor: default;
  padding: 0 5px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.remove-screenshot:hover {
  background-color: rgba(255, 68, 68, 0.2);
}

.screenshot-image {
  width: 100%;
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  max-height: 150px; /* Make images smaller */
  object-fit: contain;
}

.screenshot-controls {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

.screenshot-button,
.clear-button,
.analyze-button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  font-weight: 500;
  color: white;
  transition: background-color 0.2s;
}

.screenshot-button {
  background-color: #4c8bf5;
}

.screenshot-button:hover:not(:disabled) {
  background-color: #3b7de4;
}

.clear-button {
  background-color: #f44336;
}

.clear-button:hover:not(:disabled) {
  background-color: #e53935;
}

.analyze-button {
  background-color: #28a745;
}

.analyze-button:hover:not(:disabled) {
  background-color: #218838;
}

.shortcuts-help {
  margin-top: 20px;
  padding: 15px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
}

.shortcuts-help h3 {
  margin: 0 0 10px 0;
  font-size: 14px;
  color: #aaa;
}

.shortcuts-help ul {
  list-style: none;
  margin: 0;
  padding: 0;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 4px;
}

.shortcuts-help li {
  font-size: 10px;
  color: #888;
}

.model-selector {
  padding: 10px;
  background-color: transparent;
  width: 100%;
}

/* Remove all dropdown related styles */
.model-select {
  display: none !important;
}

/* Radio button group */
.radio-group {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: 2px;
  width: 80%;
  padding: 2px;
}

/* Radio button label */
.radio-label {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 4px;
  background-color: rgba(0, 0, 0, 0.2);
  transition: background-color 0.2s;
  min-height: 20px;
}

.radio-label:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Custom radio button */
.radio-input {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  width: 12px;
  height: 12px;
  border: 1px solid #666;
  border-radius: 50%;
  outline: none;
  margin-right: 6px;
  position: relative;
  flex-shrink: 0;
}

.radio-input:checked {
  border-color: #4c8bf5;
}

.radio-input:checked::before {
  content: '';
  position: absolute;
  width: 6px;
  height: 6px;
  background-color: #4c8bf5;
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* Radio button text */
.radio-text {
  color: #f0f0f0;
  font-size: 6px;
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.analysis-container {
  flex: 1;
  margin-top: 10px;
  padding: 0;
  background-color: #1e1e1e;
  border-radius: 8px;
  position: relative;
  width: 100%;
  display: flex;
  flex-direction: column;
  overflow: visible; /* Make content visible */
  min-height: 400px; /* Increased for more vertical space */
  max-width: 100%; /* Ensure it doesn't exceed container width */
  box-sizing: border-box; /* Include padding and border in width calculation */
  border: 1px solid #333333;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  z-index: 3;
}

.spinner {
  border: 3px solid rgba(255, 255, 255, 0.1);
  border-top: 3px solid #4c8bf5;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.analysis-result {
  background-color: #1E1E1E;
  color: #D4D4D4;
  padding: 16px;
  margin: 0;
  font-family: 'Menlo', 'Monaco', 'Courier New', monospace;
  font-size: 16px; /* Increased from 14px for better readability */
  line-height: 1.5;
  overflow: visible; /* Make content visible without scrollbars */
  flex: 1;
  width: 100%;
  box-sizing: border-box; /* Include padding in width calculation */
}

.analysis-result pre {
  margin: 0;
  padding: 0;
  height: 100%;
  white-space: pre-wrap; /* Changed from pre to pre-wrap to allow wrapping */
  word-break: break-word; /* Added to prevent overflow */
  word-wrap: break-word; /* Added to ensure text wraps */
  overflow: visible; /* Changed from auto to visible to prevent scrollbars */
  width: 100%; /* Ensure it takes full width */
}

.analysis-result code {
  font-family: 'Menlo', 'Monaco', 'Courier New', monospace;
  font-size: 16px; /* Increased from 14px for better readability */
  line-height: 1.5;
  tab-size: 4;
  display: block;
  padding-left: 0; /* Removed padding to use full width */
  width: 100%; /* Ensure it takes full width */
  box-sizing: border-box; /* Include padding in width calculation */
}

/* VSCode-style scrollbar */
.app::-webkit-scrollbar {
  width: 14px;
  height: 14px;
  background-color: #1e1e1e;
}

.app::-webkit-scrollbar-thumb {
  min-height: 40px;
  background-color: #424242;
  border: 4px solid #1e1e1e;
  background-clip: padding-box;
  border-radius: 7px;
}

.app::-webkit-scrollbar-thumb:hover {
  background-color: #686868;
}

.app::-webkit-scrollbar-track {
  background-color: #1e1e1e;
}

.app::-webkit-scrollbar-corner {
  background-color: #1e1e1e;
}

/* Solution container styles */
.solution-container {
  width: 100%;
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

.solution-content {
  width: 100%;
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

.text-content {
  width: 100%;
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

/* Make lists use full width */
.text-content ul,
.text-content ol {
  width: 100%;
  padding-left: 20px;
  margin: 0;
  box-sizing: border-box;
}

.text-content li {
  width: 100%;
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

/* Exact VS Code colors */
.token.comment {
  color: #6A9955;
}

.token.decorator {
  color: #569CD6;
}

.token.string {
  color: #CE9178;
}

.token.keyword {
  color: #C586C0;
}

.token.function {
  color: #DCDCAA;
}

.token.parameter,
.token.variable {
  color: #9CDCFE;
}

.token.operator {
  color: #D4D4D4;
}

.token.punctuation {
  color: #D4D4D4;
}

.token.class-name,
.token.builtin {
  color: #4EC9B0;
}

.token.number {
  color: #B5CEA8;
}

.token.boolean {
  color: #569CD6;
}

.token.property {
  color: #9CDCFE;
}

/* Handle Python decorators specifically */
.token.punctuation.decorator {
  color: #569CD6;
}

.token.decorator .name {
  color: #DCDCAA;
}

.token.decorator .punctuation {
  color: #569CD6;
}

.error {
  color: #ff4444;
  margin-top: 10px;
  text-align: center;
  padding: 10px;
  background-color: rgba(255, 68, 68, 0.1);
  border-radius: 4px;
}

/* Ensure all elements use default cursor */
*,
*::before,
*::after,
.prompt-container,
.button-container,
button,
input,
textarea,
select,
option,
a,
div,
span,
p,
pre,
code {
  -webkit-app-region: no-drag !important;
  cursor: default !important;
}

.control-panel {
  display: flex;
  flex-direction: column;
  gap: 15px;
  cursor: default;
}

.prompt-container {
  width: 100%;
  flex-shrink: 0;
  min-height: 50px;
  margin: 5px 0;
}

.prompt-input {
  width: 100%;
  min-height: 50px;
  padding: 6px;
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  background-color: #1e1e1e;
  color: #d4d4d4;
  font-family: 'Menlo', 'Monaco', 'Courier New', monospace;
  font-size: 14px;
  resize: vertical;
  outline: none;
  cursor: default !important;
}

.prompt-input:focus {
  border-color: #007acc;
}

.prompt-input:disabled {
  background-color: rgba(30, 30, 30, 0.5);
  color: #666666;
  cursor: default !important;
}

.prompt-input::-webkit-input-placeholder {
  cursor: default !important;
}

.prompt-input:-moz-placeholder,
.prompt-input::-moz-placeholder {
  cursor: default !important;
}

.prompt-input * {
  cursor: default !important;
}

.button-group {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
  width: 100%;
}

.screenshot-container {
  margin-top: 15px;
  background-color: rgba(0, 0, 0, 0.2);
  padding: 10px;
  border-radius: 8px;
  flex-shrink: 0;
  max-height: none;
  overflow: visible;
  width: 100%;
}

.screenshot-controls {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

.screenshot-button,
.clear-button,
.analyze-button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  font-weight: 500;
  color: white;
  transition: background-color 0.2s;
}

.screenshot-button {
  background-color: #4c8bf5;
}

.screenshot-button:hover:not(:disabled) {
  background-color: #3b7de4;
}

.clear-button {
  background-color: #f44336;
}

.clear-button:hover:not(:disabled) {
  background-color: #e53935;
}

.analyze-button {
  background-color: #28a745;
}

.analyze-button:hover:not(:disabled) {
  background-color: #218838;
}

.shortcuts-help {
  margin-top: 20px;
  padding: 15px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
}

.shortcuts-help h3 {
  margin: 0 0 10px 0;
  font-size: 14px;
  color: #aaa;
}

.shortcuts-help ul {
  list-style: none;
  margin: 0;
  padding: 0;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 8px;
}

.shortcuts-help li {
  font-size: 12px;
  color: #888;
}

.model-selector {
  width: 100%;
  min-height: 40px;
  margin-bottom: 10px;
  flex-shrink: 0;
}

.model-select {
  width: 100%;
  padding: 8px;
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  background-color: rgba(30, 30, 30, 0.9);
  color: #f0f0f0;
  font-family: inherit;
  outline: none;
  cursor: default !important;
}

.model-select option {
  background-color: rgba(30, 30, 30, 0.9);
  color: #f0f0f0;
  padding: 8px;
  cursor: default !important;
}

.model-select:focus {
  border-color: #4c8bf5;
}

.model-select:disabled {
  background-color: rgba(30, 30, 30, 0.5);
  color: #666666;
  cursor: default !important;
}

.app-header {
  background-color: #1a1a1a;
  color: white;
  padding: 8px;
  text-align: center;
  border-bottom: 1px solid #333;
  margin-bottom: 8px;
  flex-shrink: 0;
  min-height: 40px; /* Reduced height */
}

.app-header h1 {
  margin: 0;
  font-size: 1.8rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.shortcuts-info {
  display: none;
}

.shortcuts-row {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: center;
  align-items: center;
}

.shortcuts-row span {
  color: #888;
  margin-right: 4px;
}

kbd {
  background-color: #2a2a2a;
  border: 1px solid #3a3a3a;
  border-radius: 3px;
  padding: 2px 6px;
  margin: 0 2px;
  font-size: 0.9em;
  color: #d4d4d4;
  font-family: 'Menlo', 'Monaco', 'Courier New', monospace;
}

.radio-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.radio-label {
  display: flex;
  align-items: center;
  cursor: default !important;
  padding: 8px;
  border-radius: 4px;
  background-color: transparent;
  transition: background-color 0.2s;
}

.radio-label:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.radio-input {
  margin-right: 8px;
  cursor: default !important;
}

.radio-text {
  color: #f0f0f0;
  font-size: 12px;
}

/* Hide default radio button */
.radio-input {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  border: 2px solid #666;
  border-radius: 50%;
  outline: none;
  margin-right: 10px;
  position: relative;
  cursor: default !important;
}

/* Custom radio button style */
.radio-input:checked {
  border-color: #4c8bf5;
}

.radio-input:checked::before {
  content: '';
  position: absolute;
  width: 8px;
  height: 8px;
  background-color: #4c8bf5;
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}