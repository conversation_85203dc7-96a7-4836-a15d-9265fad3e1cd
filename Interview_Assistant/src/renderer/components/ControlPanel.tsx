import React from 'react';

interface ControlPanelProps {
  onTakeScreenshot: () => void;
  onClearScreenshots: () => void;
  onAnalyze: () => void;
  isAnalyzing: boolean;
  selectedModel: string;
  onModelChange: (model: string) => void;
  userPrompt: string;
  onPromptChange: (prompt: string) => void;
  screenshotCount: number;
}

const ControlPanel: React.FC<ControlPanelProps> = ({
  onTakeScreenshot,
  onClearScreenshots,
  onAnalyze,
  isAnalyzing,
  selectedModel,
  onModelChange,
  userPrompt,
  onPromptChange,
  screenshotCount
}) => {
  return (
    <div className="control-panel">
      <div className="model-selector">
        <select
          value={selectedModel}
          onChange={(e) => onModelChange(e.target.value)}
          disabled={isAnalyzing}
        >
          <option value="gemini-1.5-pro">Gemini 1.5 Pro</option>
          <option value="gemini-1.5-flash">Gemini 1.5 Flash</option>
          <option value="claude-3-7-sonnet-20250219">Claude 3 Sonnet</option>
          <option value="claude-3-5-haiku-20240307">Claude 3 Haiku</option>
        </select>
      </div>

      <div className="prompt-container">
        <textarea
          className="prompt-input"
          value={userPrompt}
          onChange={(e) => onPromptChange(e.target.value)}
          placeholder="Enter your prompt here..."
          disabled={isAnalyzing}
        />
      </div>

      <div className="screenshot-controls">
        <button 
          className="screenshot-button"
          onClick={onTakeScreenshot}
          disabled={isAnalyzing}
        >
          Take Screenshot {screenshotCount > 0 ? `(${screenshotCount})` : ''}
        </button>
        <button 
          className="clear-button"
          onClick={onClearScreenshots}
          disabled={isAnalyzing || screenshotCount === 0}
        >
          Clear All
        </button>
        <button 
          className="analyze-button"
          onClick={onAnalyze}
          disabled={isAnalyzing || screenshotCount === 0}
        >
          {isAnalyzing ? 'Analyzing...' : 'Analyze'}
        </button>
      </div>
    </div>
  );
};

export default ControlPanel; 