import React, { useState, useEffect } from 'react';

interface ModelSelectorProps {
  onModelChange: (model: string) => void;
}

const ModelSelector: React.FC<ModelSelectorProps> = ({ onModelChange }) => {
  const [selectedModel, setSelectedModel] = useState('gemini-2.0-flash');
  const [apiStatus, setApiStatus] = useState<Record<string, boolean>>({});
  const { ipcRenderer } = window.require('electron');

  const models = [
    { id: 'gemini-2.5-flash-preview-04-17', name: 'Gemini 2.5 Flash Preview', provider: 'Google' },
    { id: 'gemini-2.5-pro-exp-03-25', name: 'Gemini Pro 2.5', provider: 'Google' },
    { id: 'gemini-2.0-flash', name: 'Gemini 2.0 Flash', provider: 'Google' },
    { id: 'gemini-2.0-flash-lite', name: 'Gemini 2.0 Flash Lite', provider: 'Google' },
    { id: 'gemini-1.5-flash', name: 'Gemini 1.5 Flash', provider: 'Google' },
    { id: 'gemini-1.5-pro', name: 'Gemini 1.5 Pro', provider: 'Google' },
    { id: 'claude-3-7-sonnet-20250219', name: 'CLAUDE 3.7 Sonnet', provider: 'Anthropic' }
  ];

  console.log('Available models:', models);

  useEffect(() => {
    const checkApiStatus = async () => {
      try {
        const status = await ipcRenderer.invoke('check-api-status');
        setApiStatus(status);
      } catch (error) {
        console.error('Failed to check API status:', error);
      }
    };

    checkApiStatus();
  }, []);

  const handleModelSelect = (modelId: string) => {
    setSelectedModel(modelId);
    onModelChange(modelId);
  };

  const getProviderStatus = (provider: string) => {
    switch (provider) {
      case 'Google':
        return apiStatus.hasGeminiKey ? '' : ' (API Key Missing)';
      case 'Anthropic':
        return apiStatus.hasClaudeKey ? '' : ' (API Key Missing)';
      default:
        return '';
    }
  };

  console.log('Selected model:', selectedModel);

  return (
    <div className="model-selector">
      <div className="radio-group">
        {models.map(model => (
          <label key={model.id} className="radio-label">
            <input
              type="radio"
              name="model"
              value={model.id}
              checked={selectedModel === model.id}
              onChange={() => handleModelSelect(model.id)}
              className="radio-input"
            />
            <span className="radio-text">
              {model.name} - {model.provider}{getProviderStatus(model.provider)}
            </span>
          </label>
        ))}
      </div>
    </div>
  );
};

export default ModelSelector;