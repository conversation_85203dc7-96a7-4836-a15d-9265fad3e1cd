const fs = require('fs');
const path = require('path');

// Create directories if they don't exist
const rendererDir = path.join(__dirname, 'dist', 'renderer');
if (!fs.existsSync(rendererDir)) {
  fs.mkdirSync(rendererDir, { recursive: true });
}

// Copy HTML file
fs.copyFileSync(
  path.join(__dirname, 'src', 'renderer', 'index.html'),
  path.join(__dirname, 'dist', 'renderer', 'index.html')
);

// Copy CSS file
fs.copyFileSync(
  path.join(__dirname, 'src', 'renderer', 'styles.css'),
  path.join(__dirname, 'dist', 'renderer', 'styles.css')
);

console.log('Asset files copied successfully!'); 